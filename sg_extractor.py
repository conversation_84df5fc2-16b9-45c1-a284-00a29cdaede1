import warnings
import os
import pandas as pd
import glob
from tqdm import tqdm
from datetime import datetime

# Suppress pandas warnings
from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

def extract_sg_bounces_and_unsubs(input_directory=None):
    """
    Extract hardbounces and unsubscribes from SendGrid CSV files
    
    Args:
        input_directory (str): Directory containing SendGrid CSV files. 
                              If None, prompts user for input.
    """
    
    # Get input directory from user if not provided
    if input_directory is None:
        input_directory = input("Enter the directory path containing SendGrid CSV files: ").strip()
        if not input_directory:
            print("No directory provided. Exiting.")
            return
    
    # Validate directory exists
    if not os.path.exists(input_directory):
        print(f"Directory '{input_directory}' does not exist. Exiting.")
        return
    
    # Change to input directory
    original_dir = os.getcwd()
    os.chdir(input_directory)
    
    try:
        print(f"Processing SendGrid CSV files in: {input_directory}")
        
        # Find all CSV files in the directory
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            print("No CSV files found in the specified directory.")
            return
        
        print(f"Found {len(csv_files)} CSV file(s): {csv_files}")
        
        # Concatenate all CSV files
        print("Concatenating CSV files...")
        df_concat = pd.concat([
            pd.read_csv(f, on_bad_lines='skip', low_memory=False, dtype='unicode') 
            for f in tqdm(csv_files, desc="Reading files")
        ], ignore_index=True)
        
        # Check if required columns exist
        required_columns = ["event", "email", "reason"]

        if not all(col in df_concat.columns for col in required_columns):
            print(f"Required columns {required_columns} not found in CSV files.")
            print(f"Available columns: {list(df_concat.columns)}")
            return

        # Use the concatenated dataframe directly
        df = df_concat[required_columns].copy()
        
        # Rename email column to Email for consistency
        df.rename(columns={'email': 'Email'}, inplace=True)
        
        print(f"Total records loaded: {len(df)}")
        
        # Process Unsubscribes
        print("\nProcessing unsubscribes...")
        unsub_events = ["spamreport", "unsubscribed", "complaint"]
        df_unsubs = df[df['event'].isin(unsub_events)]
        
        # Drop unnecessary columns and duplicates
        df_unsubs_clean = df_unsubs.drop(["event", "reason"], axis=1)
        df_unsubs_clean.drop_duplicates(subset="Email", keep='first', inplace=True)

        # Add reason column with value "unsubscribed"
        df_unsubs_clean['reason'] = 'unsubscribed'

        # Convert emails to lowercase
        df_unsubs_clean['Email'] = df_unsubs_clean['Email'].str.lower()
        
        # Save unsubscribes
        unsubs_filename = "sg_unsubs.csv"
        df_unsubs_clean.to_csv(unsubs_filename, index=False, encoding='utf-8-sig')
        print(f"Unsubscribes saved: {unsubs_filename} ({len(df_unsubs_clean)} records)")
        
        # Process Bounces
        print("\nProcessing bounces...")
        bounce_events = ["bounce"]
        df_bounces = df[df['event'].isin(bounce_events)]
        
        # Define hard bounce patterns
        hard_bounce_patterns = [
            "User unknown", "service disabled", "does not exist", 
            "Address unknown", "User not found", "no mailbox", 
            "no mail-box", "no mail", "unrecognized address", 
            "mailbox is unavailable", "mailbox unavailable"
        ]
        
        # Filter hard bounces based on reason patterns
        df_hard_bounces = df_bounces[
            df_bounces["reason"].str.contains("|".join(hard_bounce_patterns), 
                                            case=False, na=False)
        ]
        
        # Clean hard bounces data
        df_hard_bounces_clean = df_hard_bounces.drop(["event", "reason"], axis=1)
        df_hard_bounces_clean.drop_duplicates(subset="Email", keep='first', inplace=True)

        # Add reason column with value "hardbounce"
        df_hard_bounces_clean['reason'] = 'hardbounce'

        # Convert emails to lowercase
        df_hard_bounces_clean['Email'] = df_hard_bounces_clean['Email'].str.lower()
        
        # Save hard bounces
        hard_bounces_filename = "sg_hb.csv"
        df_hard_bounces_clean.to_csv(hard_bounces_filename, index=False, encoding='utf-8-sig')
        print(f"Hard bounces saved: {hard_bounces_filename} ({len(df_hard_bounces_clean)} records)")
        
        # Process Soft Bounces (bounces that are not hard bounces) - for counting only
        print("\nProcessing soft bounces...")
        df_soft_bounces = df_bounces[~(df_bounces.Email.isin(df_hard_bounces.Email))]

        # Clean soft bounces data for counting
        df_soft_bounces_clean = df_soft_bounces.drop(["event", "reason"], axis=1)
        df_soft_bounces_clean.drop_duplicates(subset="Email", keep='first', inplace=True)

        print(f"Soft bounces processed: {len(df_soft_bounces_clean)} records (not saved)")
        
        # Summary
        print("\n" + "="*50)
        print("PROCESSING SUMMARY")
        print("="*50)
        print(f"Input directory: {input_directory}")
        print(f"CSV files processed: {len(csv_files)}")
        print(f"Total records: {len(df)}")
        print(f"Unsubscribes: {len(df_unsubs_clean)}")
        print(f"Hard bounces: {len(df_hard_bounces_clean)}")
        print(f"Soft bounces: {len(df_soft_bounces_clean)} (not saved)")
        print("\nOutput files created:")
        print(f"- {unsubs_filename}")
        print(f"- {hard_bounces_filename}")
        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        
    finally:
        # Return to original directory
        os.chdir(original_dir)

def main():
    """Main function to run the SendGrid extractor"""
    print("SendGrid Bounce and Unsubscribe Extractor")
    print("="*45)
    
    # Option to process current directory or specify path
    choice = input("Process current directory? (y/n): ").strip().lower()
    
    if choice == 'y':
        input_dir = os.getcwd()
    else:
        input_dir = None
    
    extract_sg_bounces_and_unsubs(input_dir)
    
    print("\nProcessing complete!")

if __name__ == "__main__":
    main()
